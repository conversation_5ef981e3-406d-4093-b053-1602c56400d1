#!/usr/bin/env python3
"""
Test Inference Script for Original SDCNet (without masks)
Performs inference on the entire test set and saves predictions for video comparison.
"""

import os
import sys
import argparse
import cv2
import numpy as np
from PIL import Image
import torch
import torch.nn as nn
from torch.utils.data import DataLoader
from tqdm import tqdm
import json
from datetime import datetime
import glob

# Add current directory to path
sys.path.append('.')

from models.sdc_net2d import SDCNet2D
from datasets.frame_loader_original import FrameLoaderOriginal


def create_args():
    parser = argparse.ArgumentParser(description='SDCNet Original Inference')

    # Model parameters
    parser.add_argument('--model', default='SDCNet2D', type=str)
    parser.add_argument('--dataset', default='FrameLoaderOriginal', type=str)
    parser.add_argument('--sequence_length', default=2, type=int)
    parser.add_argument('--rgb_max', default=255.0, type=float)
    parser.add_argument('--flownet2_checkpoint',
                       default='./flownet2_pytorch/FlowNet2_checkpoint.pth.tar', type=str)

    # FlowNet2 parameters
    parser.add_argument('--fp16', action='store_true', help='Use fp16 for FlowNet2')

    # Inference parameters
    parser.add_argument('--checkpoint', required=True, type=str,
                       help='Path to trained model checkpoint')
    parser.add_argument('--test_dir', required=True, type=str,
                       help='Path to test dataset directory')
    parser.add_argument('--output_dir', default='./test_results', type=str,
                       help='Output directory for inference results')
    parser.add_argument('--batch_size', default=1, type=int,
                       help='Batch size for inference (recommended: 1)')
    parser.add_argument('--workers', default=2, type=int,
                       help='Number of data loading workers')

    # Dataset parameters
    parser.add_argument('--sample_rate', default=1, type=int)
    parser.add_argument('--crop_size', default=[256, 320], nargs=2, type=int)
    parser.add_argument('--start_index', default=0, type=int)
    parser.add_argument('--stride', default=64, type=int)

    # Device parameters
    parser.add_argument('--gpu', default=0, type=int, help='GPU device ID')

    # Output control
    parser.add_argument('--save_predictions', action='store_true', default=True,
                       help='Save prediction images')
    parser.add_argument('--save_comparisons', action='store_true', default=True,
                       help='Save side-by-side comparison images')
    parser.add_argument('--max_videos', default=None, type=int,
                       help='Limit number of videos to process (for testing)')

    return parser.parse_args()


def setup_device(args):
    """Setup GPU device"""
    if torch.cuda.is_available():
        device = torch.device(f'cuda:{args.gpu}')
        print(f"Using GPU {args.gpu}: {torch.cuda.get_device_name(args.gpu)}")
    else:
        device = torch.device('cpu')
        print("CUDA not available, using CPU")

    return device


def load_model(args, device):
    """Load trained model from checkpoint"""
    print(f"🔍 Loading original SDCNet model from: {args.checkpoint}")

    # Create model
    model = SDCNet2D(args)
    model = model.to(device)

    # Load checkpoint
    try:
        checkpoint = torch.load(args.checkpoint, map_location=device)

        # Handle different checkpoint formats
        if 'model_state_dict' in checkpoint:
            model.load_state_dict(checkpoint['model_state_dict'])
            epoch = checkpoint.get('epoch', 'unknown')
            print(f"✅ Loaded checkpoint from epoch {epoch}")
        else:
            model.load_state_dict(checkpoint)
            print(f"✅ Loaded checkpoint (legacy format)")

        # Check if training args are available
        if 'args' in checkpoint:
            training_args = checkpoint['args']
            print(f"📊 Training configuration found:")
            print(f"   - Batch size: {training_args.get('batch_size', 'unknown')}")
            print(f"   - Learning rate: {training_args.get('lr', 'unknown')}")
            print(f"   - Skip augmentation: {training_args.get('skip_augmentation', False)}")
        else:
            print(f"⚠️  No training args found in checkpoint")

    except Exception as e:
        print(f"❌ Failed to load model: {e}")
        return None

    model.eval()
    return model


def setup_dataset(args):
    """Setup test dataset"""
    print(f"📁 Setting up test dataset from: {args.test_dir}")

    # Disable skip augmentation for inference (always use consecutive sequences)
    args.skip_augmentation = False

    try:
        dataset = FrameLoaderOriginal(args, args.test_dir, is_training=False)
        dataloader = DataLoader(
            dataset,
            batch_size=args.batch_size,
            shuffle=False,
            num_workers=args.workers,
            pin_memory=False,  # Disable to save GPU memory
            drop_last=False
        )

        print(f"✅ Dataset loaded successfully")
        print(f"   - Total samples: {len(dataset)}")
        print(f"   - Total batches: {len(dataloader)}")

        return dataset, dataloader

    except Exception as e:
        print(f"❌ Failed to load dataset: {e}")
        return None, None


def create_output_directories(args, video_names):
    """Create output directory structure organized by video"""
    timestamp = datetime.now().strftime("%Y_%m_%d_%H_%M_%S")
    output_dir = os.path.join(args.output_dir, f"inference_{timestamp}")

    # Create main directories
    os.makedirs(output_dir, exist_ok=True)
    os.makedirs(os.path.join(output_dir, "videos"), exist_ok=True)

    # Create directories for each video
    for video_name in video_names:
        video_dir = os.path.join(output_dir, video_name)
        os.makedirs(video_dir, exist_ok=True)

        # Create video output directory
        video_output_dir = os.path.join(output_dir, "videos", video_name)
        os.makedirs(video_output_dir, exist_ok=True)

    print(f"📂 Output directory: {output_dir}")
    print(f"📂 Created directories for {len(video_names)} videos")

    return output_dir


def save_image(image_tensor, filepath):
    """Save image tensor as PNG file with correct color space"""
    # Convert tensor to numpy
    if isinstance(image_tensor, torch.Tensor):
        image_np = image_tensor.cpu().numpy()
    else:
        image_np = image_tensor

    # Handle different tensor formats
    if len(image_np.shape) == 4:  # Batch dimension
        image_np = image_np[0]

    if image_np.shape[0] == 3:  # Channel first
        image_np = image_np.transpose(1, 2, 0)

    # Ensure valid range
    image_np = np.clip(image_np, 0, 255).astype(np.uint8)

    # Convert BGR to RGB (OpenCV uses BGR, but we want RGB for saving)
    if len(image_np.shape) == 3 and image_np.shape[2] == 3:
        image_np = cv2.cvtColor(image_np, cv2.COLOR_BGR2RGB)

    # Save using PIL for better quality
    image_pil = Image.fromarray(image_np)
    image_pil.save(filepath)


def create_comparison_image(input_img, prediction_img, target_img):
    """Create side-by-side comparison image"""
    # Ensure all images have the same shape
    h, w = target_img.shape[:2]

    # Resize if needed
    if input_img.shape[:2] != (h, w):
        input_img = cv2.resize(input_img, (w, h))
    if prediction_img.shape[:2] != (h, w):
        prediction_img = cv2.resize(prediction_img, (w, h))

    # Create comparison (Input | Prediction | Target)
    comparison = np.hstack([input_img, prediction_img, target_img])

    return comparison


def get_video_names_from_dataset(dataset):
    """Extract video names from dataset"""
    video_names = []
    if hasattr(dataset, 'ref'):
        for video_frames in dataset.ref:
            video_name = os.path.basename(os.path.dirname(video_frames[0]))
            video_names.append(video_name)
    return video_names


def get_frame_number_from_path(frame_path):
    """Extract frame number from file path, handling both 5 and 6 digit formats"""
    filename = os.path.basename(frame_path)
    name_without_ext = os.path.splitext(filename)[0]

    # Try to extract number from end of filename
    import re
    match = re.search(r'(\d{5,6})$', name_without_ext)
    if match:
        return int(match.group(1))

    # Fallback: try to find any number
    match = re.search(r'(\d+)', name_without_ext)
    if match:
        return int(match.group(1))

    return 0


def run_inference(model, dataloader, output_dir, args, device):
    """Run inference on the entire dataset, organized by video"""
    print(f"🚀 Starting inference...")

    model.eval()
    video_results = {}
    total_samples = 0
    successful_samples = 0

    # Get video information from dataset
    video_info = {}
    if hasattr(dataloader.dataset, 'ref'):
        for i, video_frames in enumerate(dataloader.dataset.ref):
            video_name = os.path.basename(os.path.dirname(video_frames[0]))
            video_info[i] = {
                'name': video_name,
                'frames': video_frames,
                'frame_count': len(video_frames),
                'processed_frames': []
            }

    current_video_idx = 0
    current_frame_in_video = 0

    with torch.no_grad():
        pbar = tqdm(dataloader, desc="Processing samples")

        for batch_idx, batch in enumerate(pbar):
            try:
                # Move to device
                inputs = {}
                for key in ['image']:
                    if key in batch:
                        inputs[key] = [tensor.to(device) for tensor in batch[key]]

                # Get input file paths to determine video and frame info
                input_files = batch.get('input_files', [])

                # Forward pass
                losses, prediction, target = model(inputs)

                # Process each sample in batch
                batch_size = prediction.shape[0]
                for i in range(batch_size):
                    try:
                        # Determine video name and frame number
                        if input_files and len(input_files) > i:
                            # Get the target frame path (last in sequence)
                            target_frame_path = input_files[i][-1] if isinstance(input_files[i], list) else input_files[i]
                            video_name = os.path.basename(os.path.dirname(target_frame_path))
                            frame_number = get_frame_number_from_path(target_frame_path)
                        else:
                            # Fallback: use video info
                            if current_video_idx < len(video_info):
                                video_name = video_info[current_video_idx]['name']
                                frame_number = current_frame_in_video + 2  # Start from frame 2 (00002)
                            else:
                                video_name = f"unknown_video_{current_video_idx}"
                                frame_number = current_frame_in_video + 2

                        # Get prediction image
                        pred_img = prediction[i]

                        # Convert to numpy for saving
                        pred_np = pred_img.cpu().numpy().transpose(1, 2, 0)

                        # Ensure valid range
                        pred_np = np.clip(pred_np, 0, 255).astype(np.uint8)

                        # Fix color space (convert BGR to RGB if needed)
                        if len(pred_np.shape) == 3 and pred_np.shape[2] == 3:
                            pred_np = cv2.cvtColor(pred_np, cv2.COLOR_BGR2RGB)

                        # Create filename with proper number of digits
                        if frame_number < 100000:
                            frame_filename = f"{frame_number:05d}.png"
                        else:
                            frame_filename = f"{frame_number:06d}.png"

                        # Save prediction in video-specific directory
                        video_dir = os.path.join(output_dir, video_name)
                        pred_path = os.path.join(video_dir, frame_filename)
                        save_image(pred_np, pred_path)

                        # Track results
                        if video_name not in video_results:
                            video_results[video_name] = {
                                'frames': [],
                                'count': 0
                            }

                        video_results[video_name]['frames'].append({
                            'frame_number': frame_number,
                            'filename': frame_filename,
                            'path': pred_path
                        })
                        video_results[video_name]['count'] += 1

                        successful_samples += 1
                        current_frame_in_video += 1

                    except Exception as e:
                        print(f"❌ Error processing sample {i} in batch {batch_idx}: {e}")
                        continue

                total_samples += batch_size

                # Update progress
                pbar.set_postfix({
                    'Videos': len(video_results),
                    'Frames': successful_samples,
                    'Success Rate': f"{successful_samples/total_samples*100:.1f}%"
                })

                # Limit processing if specified
                if args.max_videos and len(video_results) >= args.max_videos:
                    print(f"Reached max_videos limit: {args.max_videos}")
                    break

            except Exception as e:
                print(f"❌ Error processing batch {batch_idx}: {e}")
                continue

    # Save results summary
    results = {
        'total_samples': total_samples,
        'successful_samples': successful_samples,
        'success_rate': successful_samples / total_samples if total_samples > 0 else 0,
        'total_videos': len(video_results),
        'video_results': video_results,
        'checkpoint': args.checkpoint,
        'test_dir': args.test_dir,
        'output_dir': output_dir,
        'timestamp': datetime.now().isoformat(),
        'model': 'SDCNet2D_Original'
    }

    results_file = os.path.join(output_dir, 'inference_results.json')
    with open(results_file, 'w') as f:
        json.dump(results, f, indent=2)

    print(f"\n✅ Inference completed!")
    print(f"   - Total videos: {len(video_results)}")
    print(f"   - Total frames: {successful_samples}")
    print(f"   - Success rate: {successful_samples/total_samples*100:.1f}%")
    print(f"   - Results saved to: {results_file}")

    # Print video summary
    print(f"\n📊 Video Summary:")
    for video_name, info in video_results.items():
        print(f"   - {video_name}: {info['count']} frames")

    return results


def main():
    args = create_args()

    print("🚀 Original SDCNet Inference")
    print("=" * 50)
    print(f"Device: cuda:{args.gpu}" if torch.cuda.is_available() else "Device: cpu")

    # Setup device
    device = setup_device(args)

    # Load model
    model = load_model(args, device)
    if model is None:
        return

    # Setup dataset
    dataset, dataloader = setup_dataset(args)
    if dataset is None or dataloader is None:
        return

    # Get video names for directory structure
    video_names = get_video_names_from_dataset(dataset)
    print(f"📹 Found {len(video_names)} videos: {', '.join(video_names[:5])}{'...' if len(video_names) > 5 else ''}")

    # Create output directories
    output_dir = create_output_directories(args, video_names)

    # Run inference
    results = run_inference(model, dataloader, output_dir, args, device)

    print(f"\n🎬 Next steps:")
    print(f"1. Create comparison videos:")
    print(f"   python create_comparison_videos_original.py --inference_dir {output_dir}")
    print(f"2. Check results in: {output_dir}")


if __name__ == "__main__":
    main()
