#!/usr/bin/env python3
"""
Test Inference Script for Original SDCNet (without masks)
Performs inference on the entire test set and saves predictions for video comparison.
"""

import os
import sys
import argparse
import cv2
import numpy as np
from PIL import Image
import torch
import torch.nn as nn
from torch.utils.data import DataLoader
from tqdm import tqdm
import json
from datetime import datetime
import glob

# Add current directory to path
sys.path.append('.')

from models.sdc_net2d import SDCNet2D
from datasets.frame_loader_original import FrameLoaderOriginal


def create_args():
    parser = argparse.ArgumentParser(description='SDCNet Original Inference')

    # Model parameters
    parser.add_argument('--model', default='SDCNet2D', type=str)
    parser.add_argument('--dataset', default='FrameLoaderOriginal', type=str)
    parser.add_argument('--sequence_length', default=2, type=int)
    parser.add_argument('--rgb_max', default=255.0, type=float)
    parser.add_argument('--flownet2_checkpoint',
                       default='./flownet2_pytorch/FlowNet2_checkpoint.pth.tar', type=str)

    # FlowNet2 parameters
    parser.add_argument('--fp16', action='store_true', help='Use fp16 for FlowNet2')

    # Inference parameters
    parser.add_argument('--checkpoint', required=True, type=str,
                       help='Path to trained model checkpoint')
    parser.add_argument('--test_dir', required=True, type=str,
                       help='Path to test dataset directory')
    parser.add_argument('--output_dir', default='./test_results', type=str,
                       help='Output directory for inference results')
    parser.add_argument('--batch_size', default=1, type=int,
                       help='Batch size for inference (recommended: 1)')
    parser.add_argument('--workers', default=2, type=int,
                       help='Number of data loading workers')

    # Dataset parameters
    parser.add_argument('--sample_rate', default=1, type=int)
    parser.add_argument('--crop_size', default=[256, 320], nargs=2, type=int)
    parser.add_argument('--start_index', default=0, type=int)
    parser.add_argument('--stride', default=64, type=int)

    # Device parameters
    parser.add_argument('--gpu', default=0, type=int, help='GPU device ID')

    # Output control
    parser.add_argument('--save_predictions', action='store_true', default=True,
                       help='Save prediction images')
    parser.add_argument('--save_comparisons', action='store_true', default=True,
                       help='Save side-by-side comparison images')
    parser.add_argument('--max_videos', default=None, type=int,
                       help='Limit number of videos to process (for testing)')

    return parser.parse_args()


def setup_device(args):
    """Setup GPU device"""
    if torch.cuda.is_available():
        device = torch.device(f'cuda:{args.gpu}')
        print(f"Using GPU {args.gpu}: {torch.cuda.get_device_name(args.gpu)}")
    else:
        device = torch.device('cpu')
        print("CUDA not available, using CPU")
    
    return device


def load_model(args, device):
    """Load trained model from checkpoint"""
    print(f"🔍 Loading original SDCNet model from: {args.checkpoint}")
    
    # Create model
    model = SDCNet2D(args)
    model = model.to(device)
    
    # Load checkpoint
    try:
        checkpoint = torch.load(args.checkpoint, map_location=device)
        
        # Handle different checkpoint formats
        if 'model_state_dict' in checkpoint:
            model.load_state_dict(checkpoint['model_state_dict'])
            epoch = checkpoint.get('epoch', 'unknown')
            print(f"✅ Loaded checkpoint from epoch {epoch}")
        else:
            model.load_state_dict(checkpoint)
            print(f"✅ Loaded checkpoint (legacy format)")
            
        # Check if training args are available
        if 'args' in checkpoint:
            training_args = checkpoint['args']
            print(f"📊 Training configuration found:")
            print(f"   - Batch size: {training_args.get('batch_size', 'unknown')}")
            print(f"   - Learning rate: {training_args.get('lr', 'unknown')}")
            print(f"   - Skip augmentation: {training_args.get('skip_augmentation', False)}")
        else:
            print(f"⚠️  No training args found in checkpoint")
            
    except Exception as e:
        print(f"❌ Failed to load model: {e}")
        return None
    
    model.eval()
    return model


def setup_dataset(args):
    """Setup test dataset"""
    print(f"📁 Setting up test dataset from: {args.test_dir}")
    
    # Disable skip augmentation for inference (always use consecutive sequences)
    args.skip_augmentation = False
    
    try:
        dataset = FrameLoaderOriginal(args, args.test_dir, is_training=False)
        dataloader = DataLoader(
            dataset,
            batch_size=args.batch_size,
            shuffle=False,
            num_workers=args.workers,
            pin_memory=False,  # Disable to save GPU memory
            drop_last=False
        )
        
        print(f"✅ Dataset loaded successfully")
        print(f"   - Total samples: {len(dataset)}")
        print(f"   - Total batches: {len(dataloader)}")
        
        return dataset, dataloader
        
    except Exception as e:
        print(f"❌ Failed to load dataset: {e}")
        return None, None


def create_output_directories(args):
    """Create output directory structure"""
    timestamp = datetime.now().strftime("%Y_%m_%d_%H_%M_%S")
    output_dir = os.path.join(args.output_dir, f"inference_{timestamp}")
    
    # Create directories
    os.makedirs(output_dir, exist_ok=True)
    os.makedirs(os.path.join(output_dir, "predictions"), exist_ok=True)
    os.makedirs(os.path.join(output_dir, "comparisons"), exist_ok=True)
    os.makedirs(os.path.join(output_dir, "targets"), exist_ok=True)
    
    print(f"📂 Output directory: {output_dir}")
    
    return output_dir


def save_image(image_tensor, filepath):
    """Save image tensor as PNG file"""
    # Convert tensor to numpy
    if isinstance(image_tensor, torch.Tensor):
        image_np = image_tensor.cpu().numpy()
    else:
        image_np = image_tensor
    
    # Handle different tensor formats
    if len(image_np.shape) == 4:  # Batch dimension
        image_np = image_np[0]
    
    if image_np.shape[0] == 3:  # Channel first
        image_np = image_np.transpose(1, 2, 0)
    
    # Ensure valid range
    image_np = np.clip(image_np, 0, 255).astype(np.uint8)
    
    # Save using PIL for better quality
    image_pil = Image.fromarray(image_np)
    image_pil.save(filepath)


def create_comparison_image(input_img, prediction_img, target_img):
    """Create side-by-side comparison image"""
    # Ensure all images have the same shape
    h, w = target_img.shape[:2]
    
    # Resize if needed
    if input_img.shape[:2] != (h, w):
        input_img = cv2.resize(input_img, (w, h))
    if prediction_img.shape[:2] != (h, w):
        prediction_img = cv2.resize(prediction_img, (w, h))
    
    # Create comparison (Input | Prediction | Target)
    comparison = np.hstack([input_img, prediction_img, target_img])
    
    return comparison


def run_inference(model, dataloader, output_dir, args, device):
    """Run inference on the entire dataset"""
    print(f"🚀 Starting inference...")
    
    model.eval()
    results = {}
    total_samples = 0
    successful_samples = 0
    
    # Get video information from dataset
    video_info = {}
    if hasattr(dataloader.dataset, 'ref'):
        for i, video_frames in enumerate(dataloader.dataset.ref):
            video_name = os.path.basename(os.path.dirname(video_frames[0]))
            video_info[i] = {
                'name': video_name,
                'frames': len(video_frames),
                'processed': 0
            }
    
    with torch.no_grad():
        pbar = tqdm(dataloader, desc="Processing samples")
        
        for batch_idx, batch in enumerate(pbar):
            try:
                # Move to device
                inputs = {}
                for key in ['image']:
                    if key in batch:
                        inputs[key] = [tensor.to(device) for tensor in batch[key]]
                
                # Forward pass
                losses, prediction, target = model(inputs)
                
                # Process each sample in batch
                batch_size = prediction.shape[0]
                for i in range(batch_size):
                    sample_idx = batch_idx * args.batch_size + i
                    
                    # Get images
                    input_img = inputs['image'][-2][i]  # t-1 frame (last input)
                    pred_img = prediction[i]
                    target_img = target[i]
                    
                    # Convert to numpy for saving
                    input_np = input_img.cpu().numpy().transpose(1, 2, 0)
                    pred_np = pred_img.cpu().numpy().transpose(1, 2, 0)
                    target_np = target_img.cpu().numpy().transpose(1, 2, 0)
                    
                    # Ensure valid range
                    input_np = np.clip(input_np, 0, 255).astype(np.uint8)
                    pred_np = np.clip(pred_np, 0, 255).astype(np.uint8)
                    target_np = np.clip(target_np, 0, 255).astype(np.uint8)
                    
                    # Save individual images
                    if args.save_predictions:
                        pred_path = os.path.join(output_dir, "predictions", f"pred_{sample_idx:06d}.png")
                        save_image(pred_np, pred_path)
                        
                        target_path = os.path.join(output_dir, "targets", f"target_{sample_idx:06d}.png")
                        save_image(target_np, target_path)
                    
                    # Save comparison
                    if args.save_comparisons:
                        comparison = create_comparison_image(input_np, pred_np, target_np)
                        comp_path = os.path.join(output_dir, "comparisons", f"comparison_{sample_idx:06d}.png")
                        save_image(comparison, comp_path)
                    
                    successful_samples += 1
                
                total_samples += batch_size
                
                # Update progress
                pbar.set_postfix({
                    'Processed': f"{successful_samples}/{total_samples}",
                    'Success Rate': f"{successful_samples/total_samples*100:.1f}%"
                })
                
                # Limit processing if specified
                if args.max_videos and batch_idx >= args.max_videos:
                    print(f"Reached max_videos limit: {args.max_videos}")
                    break
                    
            except Exception as e:
                print(f"❌ Error processing batch {batch_idx}: {e}")
                continue
    
    # Save results summary
    results = {
        'total_samples': total_samples,
        'successful_samples': successful_samples,
        'success_rate': successful_samples / total_samples if total_samples > 0 else 0,
        'checkpoint': args.checkpoint,
        'test_dir': args.test_dir,
        'output_dir': output_dir,
        'timestamp': datetime.now().isoformat(),
        'model': 'SDCNet2D_Original'
    }
    
    results_file = os.path.join(output_dir, 'inference_results.json')
    with open(results_file, 'w') as f:
        json.dump(results, f, indent=2)
    
    print(f"\n✅ Inference completed!")
    print(f"   - Total samples: {total_samples}")
    print(f"   - Successful: {successful_samples}")
    print(f"   - Success rate: {successful_samples/total_samples*100:.1f}%")
    print(f"   - Results saved to: {results_file}")
    
    return results


def main():
    args = create_args()
    
    print("🚀 Original SDCNet Inference")
    print("=" * 50)
    print(f"Device: cuda:{args.gpu}" if torch.cuda.is_available() else "Device: cpu")
    
    # Setup device
    device = setup_device(args)
    
    # Load model
    model = load_model(args, device)
    if model is None:
        return
    
    # Setup dataset
    dataset, dataloader = setup_dataset(args)
    if dataset is None or dataloader is None:
        return
    
    # Create output directories
    output_dir = create_output_directories(args)
    
    # Run inference
    results = run_inference(model, dataloader, output_dir, args, device)
    
    print(f"\n🎬 Next steps:")
    print(f"1. Create comparison videos:")
    print(f"   python create_comparison_videos_original.py --inference_dir {output_dir}")
    print(f"2. Check results in: {output_dir}")


if __name__ == "__main__":
    main()
