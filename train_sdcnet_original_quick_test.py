#!/usr/bin/env python3
"""
Quick test training script for original SDCNet2D (without masks)
This script runs a minimal training session to verify everything works correctly.
"""

import os
import sys
import argparse
import torch
import torch.nn as nn
import torch.optim as optim
from torch.utils.data import DataLoader
import numpy as np
from tqdm import tqdm
from datetime import datetime

# Add current directory to path
sys.path.append('.')

from models.sdc_net2d import SDCNet2D
from datasets.frame_loader_original import FrameLoaderOriginal


def create_args():
    parser = argparse.ArgumentParser(description='SDCNet2D Original Quick Test')

    # Model parameters
    parser.add_argument('--model', default='SDCNet2D', type=str)
    parser.add_argument('--dataset', default='FrameLoaderOriginal', type=str)
    parser.add_argument('--sequence_length', default=2, type=int)
    parser.add_argument('--rgb_max', default=255.0, type=float)
    parser.add_argument('--flownet2_checkpoint',
                       default='./flownet2_pytorch/FlowNet2_checkpoint.pth.tar', type=str)

    # Quick test parameters (reduced for fast testing)
    parser.add_argument('--epochs', default=2, type=int, help='Quick test: only 2 epochs')
    parser.add_argument('--batch_size', default=2, type=int, help='Quick test: small batch size')
    parser.add_argument('--val_batch_size', default=1, type=int)
    parser.add_argument('--lr', default=0.0001, type=float)
    parser.add_argument('--weight_decay', default=1e-4, type=float)
    parser.add_argument('--workers', default=2, type=int, help='Quick test: fewer workers')

    # Dataset parameters
    parser.add_argument('--train_file', required=True, type=str)
    parser.add_argument('--val_file', required=True, type=str)
    parser.add_argument('--sample_rate', default=1, type=int)
    parser.add_argument('--crop_size', default=[256, 320], nargs=2, type=int)
    parser.add_argument('--start_index', default=0, type=int)
    parser.add_argument('--stride', default=64, type=int)

    # Skip augmentation
    parser.add_argument('--skip_augmentation', action='store_true',
                       help='Enable skip frame augmentation (t-2,t->t+2)')

    # Quick test control
    parser.add_argument('--save_dir', default='./quick_test_checkpoints', type=str)
    parser.add_argument('--name', default='sdcnet_original_quick_test', type=str)
    parser.add_argument('--gpu', default=0, type=int)
    parser.add_argument('--max_train_batches', default=10, type=int, help='Quick test: limit training batches')
    parser.add_argument('--max_val_batches', default=5, type=int, help='Quick test: limit validation batches')

    return parser.parse_args()


def setup_device(args):
    """Setup GPU device"""
    if torch.cuda.is_available():
        device = torch.device(f'cuda:{args.gpu}')
        print(f"Using GPU {args.gpu}: {torch.cuda.get_device_name(args.gpu)}")
    else:
        device = torch.device('cpu')
        print("CUDA not available, using CPU")
    
    return device


def setup_model(args, device):
    """Setup model and move to device"""
    print("Setting up SDCNet2D model...")
    
    model = SDCNet2D(args)
    model = model.to(device)
    
    # Count parameters
    total_params = sum(p.numel() for p in model.parameters())
    trainable_params = sum(p.numel() for p in model.parameters() if p.requires_grad)
    
    print(f"Total parameters: {total_params:,}")
    print(f"Trainable parameters: {trainable_params:,}")
    
    return model


def setup_data_loaders(args):
    """Setup training and validation data loaders"""
    print("Setting up data loaders...")
    
    # Training dataset
    train_dataset = FrameLoaderOriginal(args, args.train_file, is_training=True)
    train_loader = DataLoader(
        train_dataset,
        batch_size=args.batch_size,
        shuffle=True,
        num_workers=args.workers,
        pin_memory=True,
        drop_last=True
    )
    
    # Validation dataset
    val_dataset = FrameLoaderOriginal(args, args.val_file, is_training=False)
    val_loader = DataLoader(
        val_dataset,
        batch_size=args.val_batch_size,
        shuffle=False,
        num_workers=args.workers,
        pin_memory=True,
        drop_last=False
    )
    
    print(f"Training samples: {len(train_dataset)}")
    print(f"Validation samples: {len(val_dataset)}")
    print(f"Training batches: {len(train_loader)} (will use max {args.max_train_batches})")
    print(f"Validation batches: {len(val_loader)} (will use max {args.max_val_batches})")
    
    return train_loader, val_loader


def quick_train_epoch(model, train_loader, optimizer, epoch, args, device):
    """Quick training epoch with limited batches"""
    model.train()
    
    total_loss = 0.0
    total_color = 0.0
    total_gradient = 0.0
    total_smoothness = 0.0
    
    # Limit number of batches for quick test
    limited_loader = list(train_loader)[:args.max_train_batches]
    pbar = tqdm(limited_loader, desc=f"Epoch {epoch+1}/{args.epochs} (Quick Test)")
    
    for batch_idx, batch in enumerate(pbar):
        # Move to GPU
        inputs = {}
        for key in ['image']:
            if key in batch:
                inputs[key] = [tensor.to(device) for tensor in batch[key]]

        # Forward pass
        optimizer.zero_grad()
        losses, prediction, target = model(inputs)

        # Backward pass
        total_loss_batch = losses['tot']
        total_loss_batch.backward()
        optimizer.step()

        # Update statistics
        loss_dict = {
            'total': total_loss_batch.item(),
            'color': losses['color'].item(),
            'color_gradient': losses['color_gradient'].item(),
            'flow_smoothness': losses['flow_smoothness'].item()
        }
        
        total_loss += loss_dict['total']
        total_color += loss_dict['color']
        total_gradient += loss_dict['color_gradient']
        total_smoothness += loss_dict['flow_smoothness']

        # Update progress bar
        pbar.set_postfix({
            'Loss': f"{loss_dict['total']:.4f}",
            'Color': f"{loss_dict['color']:.4f}",
            'Grad': f"{loss_dict['color_gradient']:.4f}",
            'Smooth': f"{loss_dict['flow_smoothness']:.4f}"
        })

    # Calculate averages
    num_batches = len(limited_loader)
    return {
        'total': total_loss / num_batches,
        'color': total_color / num_batches,
        'color_gradient': total_gradient / num_batches,
        'flow_smoothness': total_smoothness / num_batches
    }


def quick_validate_epoch(model, val_loader, epoch, args, device):
    """Quick validation epoch with limited batches"""
    model.eval()
    
    total_loss = 0.0
    total_color = 0.0
    total_gradient = 0.0
    total_smoothness = 0.0
    
    # Limit number of batches for quick test
    limited_loader = list(val_loader)[:args.max_val_batches]
    pbar = tqdm(limited_loader, desc=f"Validation {epoch+1} (Quick Test)")
    
    with torch.no_grad():
        for batch_idx, batch in enumerate(pbar):
            # Move to GPU
            inputs = {}
            for key in ['image']:
                if key in batch:
                    inputs[key] = [tensor.to(device) for tensor in batch[key]]

            # Forward pass
            losses, prediction, target = model(inputs)

            # Update statistics
            loss_dict = {
                'total': losses['tot'].item(),
                'color': losses['color'].item(),
                'color_gradient': losses['color_gradient'].item(),
                'flow_smoothness': losses['flow_smoothness'].item()
            }
            
            total_loss += loss_dict['total']
            total_color += loss_dict['color']
            total_gradient += loss_dict['color_gradient']
            total_smoothness += loss_dict['flow_smoothness']

            # Update progress bar
            pbar.set_postfix({
                'Loss': f"{loss_dict['total']:.4f}",
                'Color': f"{loss_dict['color']:.4f}",
                'Grad': f"{loss_dict['color_gradient']:.4f}",
                'Smooth': f"{loss_dict['flow_smoothness']:.4f}"
            })

    # Calculate averages
    num_batches = len(limited_loader)
    return {
        'total': total_loss / num_batches,
        'color': total_color / num_batches,
        'color_gradient': total_gradient / num_batches,
        'flow_smoothness': total_smoothness / num_batches
    }


def save_quick_checkpoint(model, optimizer, epoch, train_stats, val_stats, args):
    """Save quick test checkpoint"""
    os.makedirs(args.save_dir, exist_ok=True)
    
    checkpoint = {
        'epoch': epoch,
        'model_state_dict': model.state_dict(),
        'optimizer_state_dict': optimizer.state_dict(),
        'train_stats': train_stats,
        'val_stats': val_stats,
        'args': vars(args)
    }
    
    filename = f"{args.name}_epoch_{epoch:03d}.pth"
    torch.save(checkpoint, os.path.join(args.save_dir, filename))
    print(f"Saved quick test checkpoint: {filename}")


def main():
    # Parse arguments
    args = create_args()
    
    print("SDCNet2D Original QUICK TEST Training")
    print("=" * 60)
    print("⚠️  THIS IS A QUICK TEST VERSION")
    print(f"Skip augmentation: {'enabled' if args.skip_augmentation else 'disabled'}")
    print(f"Training dataset: {args.train_file}")
    print(f"Validation dataset: {args.val_file}")
    print(f"Epochs: {args.epochs} (quick test)")
    print(f"Batch size: {args.batch_size} (quick test)")
    print(f"Max train batches: {args.max_train_batches}")
    print(f"Max val batches: {args.max_val_batches}")
    print("=" * 60)
    
    # Setup device
    device = setup_device(args)
    
    # Setup model
    model = setup_model(args, device)
    
    # Setup data loaders
    train_loader, val_loader = setup_data_loaders(args)
    
    # Setup optimizer
    optimizer = optim.Adam(model.parameters(), lr=args.lr, weight_decay=args.weight_decay)
    
    # Quick training loop
    for epoch in range(args.epochs):
        print(f"\nEpoch {epoch+1}/{args.epochs}")
        print("-" * 30)

        # Train
        train_stats = quick_train_epoch(model, train_loader, optimizer, epoch, args, device)

        # Validate
        val_stats = quick_validate_epoch(model, val_loader, epoch, args, device)

        # Print epoch statistics
        print(f"\nEpoch {epoch+1} Results:")
        print(f"Train - Total: {train_stats['total']:.4f}, Color: {train_stats['color']:.4f}")
        print(f"Val   - Total: {val_stats['total']:.4f}, Color: {val_stats['color']:.4f}")

        # Save checkpoint
        save_quick_checkpoint(model, optimizer, epoch, train_stats, val_stats, args)

    print("\nQUICK TEST COMPLETED SUCCESSFULLY! ✅")
    print("=" * 60)
    print("The model can be trained successfully.")
    print("You can now run the full training script:")
    print(f"python train_sdcnet_original.py --train_file {args.train_file} --val_file {args.val_file}")


if __name__ == "__main__":
    main()
