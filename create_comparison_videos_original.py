#!/usr/bin/env python3
"""
Create comparison videos from Original SDCNet inference results.
This script creates side-by-side comparison videos showing input, prediction, and target frames.
"""

import os
import sys
import argparse
import cv2
import numpy as np
import json
import glob
import subprocess
from datetime import datetime
from tqdm import tqdm

# Add current directory to path
sys.path.append('.')


def check_ffmpeg():
    """Check if ffmpeg is available"""
    try:
        subprocess.run(['ffmpeg', '-version'], capture_output=True, check=True)
        return True
    except (subprocess.CalledProcessError, FileNotFoundError):
        return False


def get_video_sequences_from_inference(inference_dir, test_dir):
    """Extract video sequences from inference results"""
    print("🔍 Analyzing inference results...")
    
    # Load inference results
    results_file = os.path.join(inference_dir, 'inference_results.json')
    if not os.path.exists(results_file):
        print(f"❌ Inference results not found: {results_file}")
        return {}
    
    with open(results_file, 'r') as f:
        inference_results = json.load(f)
    
    print(f"✅ Loaded inference results: {inference_results['successful_samples']} samples")
    
    # Get comparison images
    comparisons_dir = os.path.join(inference_dir, 'comparisons')
    if not os.path.exists(comparisons_dir):
        print(f"❌ Comparisons directory not found: {comparisons_dir}")
        return {}
    
    comparison_files = sorted(glob.glob(os.path.join(comparisons_dir, 'comparison_*.png')))
    print(f"📁 Found {len(comparison_files)} comparison images")
    
    # Group by video sequences based on test dataset structure
    video_sequences = {}
    
    # Get video directories from test dataset
    test_x_dir = os.path.join(test_dir, 'X')
    if os.path.exists(test_x_dir):
        video_dirs = [d for d in os.listdir(test_x_dir) 
                     if os.path.isdir(os.path.join(test_x_dir, d)) 
                     and not d.endswith('_binary_ground_truth')]
        
        print(f"📂 Found {len(video_dirs)} video directories in test set")
        
        # For now, create one sequence with all comparison images
        # This is a simplified approach - in a full implementation,
        # you would map each comparison image to its source video
        if comparison_files:
            video_sequences['all_sequences'] = {
                'name': 'all_sequences',
                'frames': comparison_files,
                'num_frames': len(comparison_files)
            }
    
    return video_sequences


def create_video_with_ffmpeg(frames_list, output_video_path, fps=12, quality='medium'):
    """Create video from frame list using ffmpeg"""
    if not check_ffmpeg():
        raise RuntimeError("ffmpeg not found. Please install ffmpeg.")
    
    # Quality settings
    quality_settings = {
        'high': ['-crf', '18', '-preset', 'slow'],
        'medium': ['-crf', '23', '-preset', 'medium'],
        'low': ['-crf', '28', '-preset', 'fast']
    }
    
    # Create temporary directory for symlinks/copies
    temp_dir = os.path.join(os.path.dirname(output_video_path), 'temp_frames')
    os.makedirs(temp_dir, exist_ok=True)
    
    try:
        # Create numbered symlinks for ffmpeg
        for i, frame_path in enumerate(frames_list):
            temp_frame = os.path.join(temp_dir, f"frame_{i:05d}.png")
            if os.path.exists(temp_frame):
                os.remove(temp_frame)
            
            # Copy file instead of symlink for Windows compatibility
            import shutil
            shutil.copy2(frame_path, temp_frame)
        
        # Input pattern for frames
        input_pattern = os.path.join(temp_dir, "frame_%05d.png")
        
        # FFmpeg command
        cmd = [
            'ffmpeg',
            '-y',  # Overwrite output file
            '-framerate', str(fps),
            '-i', input_pattern,
            '-c:v', 'libx264',
            '-pix_fmt', 'yuv420p',
        ] + quality_settings.get(quality, quality_settings['medium']) + [
            output_video_path
        ]
        
        print(f"🎬 Creating video: {os.path.basename(output_video_path)}")
        result = subprocess.run(cmd, capture_output=True, text=True, check=True)
        
        # Clean up temp directory
        import shutil
        shutil.rmtree(temp_dir)
        
        return True
        
    except subprocess.CalledProcessError as e:
        print(f"❌ FFmpeg error: {e.stderr}")
        # Clean up temp directory on error
        import shutil
        if os.path.exists(temp_dir):
            shutil.rmtree(temp_dir)
        return False
    except Exception as e:
        print(f"❌ Error creating video: {e}")
        # Clean up temp directory on error
        import shutil
        if os.path.exists(temp_dir):
            shutil.rmtree(temp_dir)
        return False


def process_video_sequences(video_sequences, output_dir, fps=12, quality='medium'):
    """Process all video sequences and create comparison videos"""
    video_results = {}
    
    print(f"🎬 Creating comparison videos...")
    print(f"   - FPS: {fps}")
    print(f"   - Quality: {quality}")
    print(f"   - Output: {output_dir}")
    
    for video_name, sequence_info in tqdm(video_sequences.items(), desc="Creating videos"):
        try:
            # Create video output directory
            video_output_dir = os.path.join(output_dir, video_name)
            os.makedirs(video_output_dir, exist_ok=True)
            
            # Output video path
            video_output_path = os.path.join(video_output_dir, f"{video_name}_comparison.mp4")
            
            # Create video
            if create_video_with_ffmpeg(sequence_info['frames'], video_output_path, fps, quality):
                video_results[video_name] = {
                    'status': 'success',
                    'video_path': video_output_path,
                    'num_frames': sequence_info['num_frames'],
                    'fps': fps,
                    'quality': quality
                }
                print(f"✅ Created: {video_output_path}")
            else:
                video_results[video_name] = {
                    'status': 'failed',
                    'reason': 'video_creation_failed'
                }
                print(f"❌ Failed to create video for {video_name}")
                
        except Exception as e:
            video_results[video_name] = {
                'status': 'failed',
                'reason': str(e)
            }
            print(f"❌ Error processing {video_name}: {e}")
    
    return video_results


def create_summary_report(video_results, output_dir):
    """Create a summary report of video creation results"""
    summary = {
        'timestamp': datetime.now().isoformat(),
        'total_videos': len(video_results),
        'successful_videos': len([r for r in video_results.values() if r['status'] == 'success']),
        'failed_videos': len([r for r in video_results.values() if r['status'] == 'failed']),
        'results': video_results
    }
    
    # Save summary
    summary_file = os.path.join(output_dir, 'video_creation_summary.json')
    with open(summary_file, 'w') as f:
        json.dump(summary, f, indent=2)
    
    # Print summary
    print(f"\n📊 Video Creation Summary:")
    print(f"   - Total videos: {summary['total_videos']}")
    print(f"   - Successful: {summary['successful_videos']}")
    print(f"   - Failed: {summary['failed_videos']}")
    print(f"   - Success rate: {summary['successful_videos']/summary['total_videos']*100:.1f}%")
    print(f"   - Summary saved: {summary_file}")
    
    return summary


def main():
    parser = argparse.ArgumentParser(description='Create comparison videos from Original SDCNet inference results')
    parser.add_argument('--inference_dir', required=True,
                       help='Directory containing inference results')
    parser.add_argument('--test_dir', default='./dataset/test',
                       help='Path to test dataset directory')
    parser.add_argument('--output_dir', default=None,
                       help='Output directory for videos (default: inference_dir/videos)')
    parser.add_argument('--fps', type=int, default=12,
                       help='Video frame rate (default: 12 for slower playback)')
    parser.add_argument('--quality', choices=['low', 'medium', 'high'], default='medium',
                       help='Video quality (default: medium)')
    
    args = parser.parse_args()
    
    print("🎬 Original SDCNet Video Creator")
    print("=" * 50)
    
    # Check ffmpeg
    if not check_ffmpeg():
        print("❌ Error: ffmpeg not found. Please install ffmpeg to create videos.")
        print("   Download from: https://ffmpeg.org/download.html")
        print("   Windows: Download and add to PATH")
        print("   Linux: sudo apt install ffmpeg")
        print("   macOS: brew install ffmpeg")
        sys.exit(1)
    
    print("✅ ffmpeg found")
    
    # Validate input directory
    if not os.path.exists(args.inference_dir):
        print(f"❌ Inference directory not found: {args.inference_dir}")
        sys.exit(1)
    
    # Set output directory
    if args.output_dir is None:
        args.output_dir = os.path.join(args.inference_dir, 'videos')
    
    os.makedirs(args.output_dir, exist_ok=True)
    
    print(f"📁 Input: {args.inference_dir}")
    print(f"📁 Output: {args.output_dir}")
    print(f"🎥 Settings: {args.fps}fps, {args.quality} quality")
    
    # Get video sequences from inference results
    video_sequences = get_video_sequences_from_inference(args.inference_dir, args.test_dir)
    
    if not video_sequences:
        print("❌ No video sequences found in inference results")
        sys.exit(1)
    
    print(f"🎬 Found {len(video_sequences)} video sequences to process")
    
    # Create videos
    video_results = process_video_sequences(
        video_sequences,
        args.output_dir,
        args.fps,
        args.quality
    )
    
    # Create summary report
    summary = create_summary_report(video_results, args.output_dir)
    
    # Final message
    if summary['successful_videos'] > 0:
        print(f"\n🎉 Video creation completed!")
        print(f"   Check videos in: {args.output_dir}")
        print(f"\n💡 Tips:")
        print(f"   - Videos show: Input | Prediction | Target")
        print(f"   - Use {args.fps}fps for detailed frame-by-frame analysis")
        print(f"   - Adjust --fps for different playback speeds")
    else:
        print(f"\n❌ No videos were created successfully")
        print(f"   Check the error messages above")


if __name__ == "__main__":
    main()
