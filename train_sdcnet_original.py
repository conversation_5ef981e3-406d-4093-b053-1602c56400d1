#!/usr/bin/env python3
"""
Training script for original SDCNet2D (without masks)
Enhanced with GPU support, tqdm progress bars, early stopping, and visual inference monitoring.
"""

import os
import sys
import argparse
import torch
import torch.nn as nn
import torch.optim as optim
from torch.utils.data import DataLoader
import numpy as np
from tqdm import tqdm
import cv2
from datetime import datetime
import json
import shutil

# Add current directory to path
sys.path.append('.')

from models.sdc_net2d import SDCNet2D
from datasets.frame_loader_original import FrameLoaderOriginal
import tools


class EarlyStopping:
    def __init__(self, patience=10, min_delta=0.001):
        self.patience = patience
        self.min_delta = min_delta
        self.counter = 0
        self.best_loss = float('inf')
        self.best_model_state = None

    def __call__(self, val_loss, model):
        if val_loss < self.best_loss - self.min_delta:
            self.best_loss = val_loss
            self.counter = 0
            # Save best model state
            self.best_model_state = {k: v.cpu().clone() for k, v in model.state_dict().items()}
            return False
        else:
            self.counter += 1
            if self.counter >= self.patience:
                # Restore best model
                if self.best_model_state is not None:
                    model.load_state_dict({k: v.to(model.device if hasattr(model, 'device') else 'cpu')
                                         for k, v in self.best_model_state.items()})
                return True
        return False


def save_inference_sample(model, batch, epoch, batch_idx, save_dir, device):
    """Save a visual inference sample"""
    model.eval()
    with torch.no_grad():
        # Move to GPU
        inputs = {}
        for key in ['image']:
            if key in batch:
                inputs[key] = [tensor.to(device) for tensor in batch[key]]

        # Forward pass
        losses, prediction, target = model(inputs)

        # Convert to numpy for saving
        pred_np = prediction[0].cpu().numpy().transpose(1, 2, 0)
        target_np = target[0].cpu().numpy().transpose(1, 2, 0)
        input_np = inputs['image'][-2][0].cpu().numpy().transpose(1, 2, 0)  # t-1 frame

        # Denormalize (assuming 0-255 range)
        pred_np = np.clip(pred_np, 0, 255).astype(np.uint8)
        target_np = np.clip(target_np, 0, 255).astype(np.uint8)
        input_np = np.clip(input_np, 0, 255).astype(np.uint8)

        # Create comparison image
        comparison = np.hstack([input_np, pred_np, target_np])

        # Save
        filename = f"epoch_{epoch:03d}_batch_{batch_idx:04d}_comparison.png"
        cv2.imwrite(os.path.join(save_dir, filename), comparison)

    model.train()


def create_args():
    parser = argparse.ArgumentParser(description='SDCNet2D Original Training')

    # Model parameters
    parser.add_argument('--model', default='SDCNet2D', type=str)
    parser.add_argument('--dataset', default='FrameLoaderOriginal', type=str)
    parser.add_argument('--sequence_length', default=2, type=int)
    parser.add_argument('--rgb_max', default=255.0, type=float)
    parser.add_argument('--flownet2_checkpoint',
                       default='./flownet2_pytorch/FlowNet2_checkpoint.pth.tar', type=str)

    # FlowNet2 parameters (required)
    parser.add_argument('--fp16', action='store_true', help='Use fp16 for FlowNet2')

    # Training parameters
    parser.add_argument('--epochs', default=100, type=int)
    parser.add_argument('--batch_size', default=4, type=int)
    parser.add_argument('--val_batch_size', default=2, type=int)
    parser.add_argument('--lr', default=0.0001, type=float)
    parser.add_argument('--weight_decay', default=1e-4, type=float)
    parser.add_argument('--workers', default=4, type=int)

    # Dataset parameters
    parser.add_argument('--train_file', required=True, type=str)
    parser.add_argument('--val_file', required=True, type=str)
    parser.add_argument('--sample_rate', default=1, type=int)
    parser.add_argument('--crop_size', default=[256, 320], nargs=2, type=int)
    parser.add_argument('--start_index', default=0, type=int)
    parser.add_argument('--stride', default=64, type=int)

    # Skip augmentation
    parser.add_argument('--skip_augmentation', action='store_true',
                       help='Enable skip frame augmentation (t-2,t->t+2)')

    # Training control
    parser.add_argument('--save_dir', default='./checkpoints', type=str)
    parser.add_argument('--name', default='sdcnet_original', type=str)
    parser.add_argument('--resume', default='', type=str, help='Path to checkpoint to resume from')
    parser.add_argument('--gpu', default=0, type=int)
    parser.add_argument('--patience', default=10, type=int, help='Early stopping patience')
    parser.add_argument('--min_delta', default=0.001, type=float, help='Minimum change for early stopping')
    parser.add_argument('--save_freq', default=5, type=int, help='Save checkpoint every N epochs')

    # Inference monitoring
    parser.add_argument('--inference_batches', default=[1, 1000, 2000], nargs='+', type=int,
                       help='Batch numbers to save inference samples')
    parser.add_argument('--inference_samples', default=3, type=int,
                       help='Number of samples to save for inference monitoring')

    return parser.parse_args()


def setup_device(args):
    """Setup GPU device"""
    if torch.cuda.is_available():
        device = torch.device(f'cuda:{args.gpu}')
        print(f"Using GPU {args.gpu}: {torch.cuda.get_device_name(args.gpu)}")
        print(f"GPU Memory: {torch.cuda.get_device_properties(args.gpu).total_memory / 1e9:.1f} GB")
    else:
        device = torch.device('cpu')
        print("CUDA not available, using CPU")

    return device


def setup_model(args, device):
    """Setup model and move to device"""
    print("Setting up SDCNet2D model...")

    model = SDCNet2D(args)
    model = model.to(device)

    # Count parameters
    total_params = sum(p.numel() for p in model.parameters())
    trainable_params = sum(p.numel() for p in model.parameters() if p.requires_grad)

    print(f"Total parameters: {total_params:,}")
    print(f"Trainable parameters: {trainable_params:,}")

    return model


def setup_data_loaders(args):
    """Setup training and validation data loaders"""
    print("Setting up data loaders...")

    # Training dataset
    train_dataset = FrameLoaderOriginal(args, args.train_file, is_training=True)
    train_loader = DataLoader(
        train_dataset,
        batch_size=args.batch_size,
        shuffle=True,
        num_workers=args.workers,
        pin_memory=True,
        drop_last=True
    )

    # Validation dataset
    val_dataset = FrameLoaderOriginal(args, args.val_file, is_training=False)
    val_loader = DataLoader(
        val_dataset,
        batch_size=args.val_batch_size,
        shuffle=False,
        num_workers=args.workers,
        pin_memory=True,
        drop_last=False
    )

    print(f"Training samples: {len(train_dataset)}")
    print(f"Validation samples: {len(val_dataset)}")
    print(f"Training batches: {len(train_loader)}")
    print(f"Validation batches: {len(val_loader)}")

    return train_loader, val_loader


def setup_directories(args):
    """Setup output directories"""
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")

    # Main checkpoint directory
    checkpoint_dir = os.path.join(args.save_dir, f"{args.name}_{timestamp}")
    os.makedirs(checkpoint_dir, exist_ok=True)

    # Inference samples directory
    inference_dir = os.path.join(checkpoint_dir, "training_inference")
    os.makedirs(inference_dir, exist_ok=True)

    print(f"Checkpoint directory: {checkpoint_dir}")
    print(f"Inference directory: {inference_dir}")

    return checkpoint_dir, inference_dir


def train_epoch(model, train_loader, optimizer, epoch, args, device):
    """Train for one epoch"""
    model.train()

    total_loss = 0.0
    total_color = 0.0
    total_gradient = 0.0
    total_smoothness = 0.0

    # Setup inference monitoring
    inference_dir = os.path.join(args.save_dir, f"{args.name}_{datetime.now().strftime('%Y%m%d_%H%M%S')}", "training_inference")
    epoch_inference_dir = os.path.join(inference_dir, f"epoch_{epoch:03d}")
    os.makedirs(epoch_inference_dir, exist_ok=True)

    pbar = tqdm(train_loader, desc=f"Epoch {epoch+1}/{args.epochs}")

    for batch_idx, batch in enumerate(pbar):
        # Move to GPU
        inputs = {}
        for key in ['image']:
            if key in batch:
                inputs[key] = [tensor.to(device) for tensor in batch[key]]

        # Forward pass
        optimizer.zero_grad()
        losses, prediction, target = model(inputs)

        # Backward pass
        total_loss_batch = losses['tot']
        total_loss_batch.backward()
        optimizer.step()

        # Update statistics
        loss_dict = {
            'total': total_loss_batch.item(),
            'color': losses['color'].item(),
            'color_gradient': losses['color_gradient'].item(),
            'flow_smoothness': losses['flow_smoothness'].item()
        }

        total_loss += loss_dict['total']
        total_color += loss_dict['color']
        total_gradient += loss_dict['color_gradient']
        total_smoothness += loss_dict['flow_smoothness']

        # Update progress bar
        pbar.set_postfix({
            'Loss': f"{loss_dict['total']:.4f}",
            'Color': f"{loss_dict['color']:.4f}",
            'Grad': f"{loss_dict['color_gradient']:.4f}",
            'Smooth': f"{loss_dict['flow_smoothness']:.4f}"
        })

        # Save inference samples at specified batches
        if (batch_idx + 1) in args.inference_batches and batch_idx < args.inference_samples:
            save_inference_sample(model, batch, epoch, batch_idx, epoch_inference_dir, device)

    # Calculate averages
    num_batches = len(train_loader)
    return {
        'total': total_loss / num_batches,
        'color': total_color / num_batches,
        'color_gradient': total_gradient / num_batches,
        'flow_smoothness': total_smoothness / num_batches
    }


def validate_epoch(model, val_loader, epoch, args, device):
    """Validate for one epoch"""
    model.eval()

    total_loss = 0.0
    total_color = 0.0
    total_gradient = 0.0
    total_smoothness = 0.0

    pbar = tqdm(val_loader, desc=f"Validation {epoch+1}")

    with torch.no_grad():
        for batch_idx, batch in enumerate(pbar):
            # Move to GPU
            inputs = {}
            for key in ['image']:
                if key in batch:
                    inputs[key] = [tensor.to(device) for tensor in batch[key]]

            # Forward pass
            losses, prediction, target = model(inputs)

            # Update statistics
            loss_dict = {
                'total': losses['tot'].item(),
                'color': losses['color'].item(),
                'color_gradient': losses['color_gradient'].item(),
                'flow_smoothness': losses['flow_smoothness'].item()
            }

            total_loss += loss_dict['total']
            total_color += loss_dict['color']
            total_gradient += loss_dict['color_gradient']
            total_smoothness += loss_dict['flow_smoothness']

            # Update progress bar
            pbar.set_postfix({
                'Loss': f"{loss_dict['total']:.4f}",
                'Color': f"{loss_dict['color']:.4f}",
                'Grad': f"{loss_dict['color_gradient']:.4f}",
                'Smooth': f"{loss_dict['flow_smoothness']:.4f}"
            })

    # Calculate averages
    num_batches = len(val_loader)
    return {
        'total': total_loss / num_batches,
        'color': total_color / num_batches,
        'color_gradient': total_gradient / num_batches,
        'flow_smoothness': total_smoothness / num_batches
    }


def save_checkpoint(model, optimizer, epoch, train_stats, val_stats, args, is_best=False):
    """Save model checkpoint"""
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    checkpoint_dir = os.path.join(args.save_dir, f"{args.name}_{timestamp}")

    checkpoint = {
        'epoch': epoch,
        'model_state_dict': model.state_dict(),
        'optimizer_state_dict': optimizer.state_dict(),
        'train_stats': train_stats,
        'val_stats': val_stats,
        'args': vars(args)
    }

    # Regular checkpoint
    if (epoch + 1) % args.save_freq == 0:
        filename = f"sdcnet_original_epoch_{epoch:03d}.pth"
        torch.save(checkpoint, os.path.join(checkpoint_dir, filename))
        print(f"Saved checkpoint: {filename}")

    # Best model checkpoint
    if is_best:
        filename = f"sdcnet_original_best_epoch_{epoch:03d}_{timestamp}.pth"
        torch.save(checkpoint, os.path.join(checkpoint_dir, filename))
        print(f"Saved best model: {filename}")

        # Also save as latest best
        latest_best = os.path.join(checkpoint_dir, "sdcnet_original_best.pth")
        torch.save(checkpoint, latest_best)


def load_checkpoint(model, optimizer, checkpoint_path):
    """Load model checkpoint"""
    print(f"Loading checkpoint from {checkpoint_path}")
    checkpoint = torch.load(checkpoint_path)

    model.load_state_dict(checkpoint['model_state_dict'])
    optimizer.load_state_dict(checkpoint['optimizer_state_dict'])

    start_epoch = checkpoint['epoch'] + 1
    print(f"Resuming from epoch {start_epoch}")

    return start_epoch


def main():
    # Parse arguments
    args = create_args()

    print("SDCNet2D Original Training")
    print("=" * 60)
    print(f"Skip augmentation: {'enabled' if args.skip_augmentation else 'disabled'}")
    print(f"Training dataset: {args.train_file}")
    print(f"Validation dataset: {args.val_file}")
    print(f"Epochs: {args.epochs}")
    print(f"Batch size: {args.batch_size}")
    print(f"Learning rate: {args.lr}")
    print("=" * 60)

    # Setup device
    device = setup_device(args)

    # Setup model
    model = setup_model(args, device)

    # Setup data loaders
    train_loader, val_loader = setup_data_loaders(args)

    # Setup directories
    checkpoint_dir, inference_dir = setup_directories(args)

    # Setup optimizer
    optimizer = optim.Adam(model.parameters(), lr=args.lr, weight_decay=args.weight_decay)

    # Setup early stopping
    early_stopping = EarlyStopping(patience=args.patience, min_delta=args.min_delta)

    # Resume from checkpoint if specified
    start_epoch = 0
    if args.resume:
        start_epoch = load_checkpoint(model, optimizer, args.resume)

    # Training loop
    best_val_loss = float('inf')

    for epoch in range(start_epoch, args.epochs):
        print(f"\nEpoch {epoch+1}/{args.epochs}")
        print("-" * 50)

        # Train
        train_stats = train_epoch(model, train_loader, optimizer, epoch, args, device)

        # Validate
        val_stats = validate_epoch(model, val_loader, epoch, args, device)

        # Print epoch statistics
        print(f"\nEpoch {epoch+1} Results:")
        print(f"Train - Total: {train_stats['total']:.4f}, Color: {train_stats['color']:.4f}, "
              f"Gradient: {train_stats['color_gradient']:.4f}, Smoothness: {train_stats['flow_smoothness']:.4f}")
        print(f"Val   - Total: {val_stats['total']:.4f}, Color: {val_stats['color']:.4f}, "
              f"Gradient: {val_stats['color_gradient']:.4f}, Smoothness: {val_stats['flow_smoothness']:.4f}")

        # Check for best model
        is_best = val_stats['total'] < best_val_loss
        if is_best:
            best_val_loss = val_stats['total']

        # Save checkpoint
        if (epoch + 1) % args.save_freq == 0 or is_best:
            save_checkpoint(model, optimizer, epoch, train_stats, val_stats, args, is_best)

        # Early stopping check
        if early_stopping(val_stats['total'], model):
            print(f"\nEarly stopping triggered after {epoch+1} epochs")
            print(f"Best validation loss: {early_stopping.best_loss:.4f}")
            break

    print("\nTraining completed!")
    print(f"Best validation loss: {best_val_loss:.4f}")


if __name__ == "__main__":
    main()
